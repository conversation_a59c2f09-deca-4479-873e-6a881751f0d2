{% set name = "transformers" %}

package:
  name: "{{ name|lower }}"
  version: "{{ TRANSFORMERS_VERSION }}"

source:
  path: ../../

build:
  noarch: python

requirements:
  host:
    - python
    - pip
    - numpy >=1.17
    - dataclasses
    - huggingface_hub
    - packaging
    - filelock
    - requests
    - tqdm >=4.27
    - sacremoses
    - regex !=2019.12.17
    - protobuf
    - tokenizers >=0.11.1,!=0.11.3,<0.13
    - pyyaml >=5.1
    - safetensors
    - fsspec
  run:
    - python
    - numpy >=1.17
    - dataclasses
    - huggingface_hub
    - packaging
    - filelock
    - requests
    - tqdm >=4.27
    - sacremoses
    - regex !=2019.12.17
    - protobuf
    - tokenizers >=0.11.1,!=0.11.3,<0.13
    - pyyaml >=5.1
    - safetensors
    - fsspec

test:
  imports:
    - transformers

about:
  home: https://huggingface.co
  license: Apache License 2.0
  license_file: LICENSE
  summary: "🤗Transformers: State-of-the-art Natural Language Processing for Pytorch and TensorFlow 2.0."
