name: Update Transformers metadata

on:
  push:
    branches:
      - main
      - update_transformers_metadata*

jobs:
  build_and_package:
    runs-on: ubuntu-22.04
    defaults:
      run:
        shell: bash -l {0}

    steps:
      - uses: actions/checkout@v4

      - name: Setup environment
        run: |
          pip install --upgrade pip
          pip install datasets pandas==2.0.3
          pip install .[torch,tf,flax]

      - name: Update metadata
        run: |
          python utils/update_metadata.py --token ${{ secrets.LYSANDRE_HF_TOKEN }} --commit_sha ${{ github.sha }}
